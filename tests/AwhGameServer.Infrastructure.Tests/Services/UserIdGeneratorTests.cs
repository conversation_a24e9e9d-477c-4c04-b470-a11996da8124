using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Infrastructure.Configurations;
using AwhGameServer.Infrastructure.Persistence;
using AwhGameServer.Infrastructure.Services;

namespace AwhGameServer.Infrastructure.Tests.Services;

/// <summary>
/// Тесты для генератора идентификаторов пользователей UserIdGenerator.
/// Проверяют корректность генерации структурированных ID в формате SGNNNNNNNN,
/// последовательность номеров и thread-safety в многопоточной среде.
/// </summary>
public class UserIdGeneratorTests : IDisposable
{
    private readonly UserIdGenerator<UserId> _generator;
    private readonly IMongoClient _mongoClient;
    private readonly string _testDatabaseName;

    public UserIdGeneratorTests()
    {
        _testDatabaseName = $"test_db_{Guid.NewGuid():N}";
        _mongoClient = new MongoClient("mongodb://localhost:27017");

        var config = new UserIdGeneratorConfig
        {
            ServerCode = 7,
            UserGeneration = 1,
            CounterKey = "test_counter"
        };

        var usersDataDbConfig = new UsersDataDbConfig
        {
            DatabaseName = _testDatabaseName
        };

        var configOptions = Options.Create(config);
        var dbConfigOptions = Options.Create(usersDataDbConfig);

        _generator = new UserIdGenerator<UserId>(configOptions, _mongoClient, dbConfigOptions);
    }

    [Fact(DisplayName = "Первый вызов генератора возвращает корректный формат ID")]
    public async Task New_FirstCall_ReturnsCorrectFormat()
    {
        var userId = await _generator.New();

        Assert.Equal("7100000001", userId.Value);
    }

    [Fact(DisplayName = "Множественные вызовы генератора возвращают последовательные ID")]
    public async Task New_MultipleCalls_ReturnsSequentialIds()
    {
        var userId1 = await _generator.New();
        var userId2 = await _generator.New();
        var userId3 = await _generator.New();

        Assert.Equal("7100000001", userId1.Value);
        Assert.Equal("7100000002", userId2.Value);
        Assert.Equal("7100000003", userId3.Value);
    }

    [Fact(DisplayName = "Генератор с другим кодом сервера возвращает корректный формат")]
    public async Task New_DifferentServerCode_ReturnsCorrectFormat()
    {
        var config = new UserIdGeneratorConfig
        {
            ServerCode = 3,
            UserGeneration = 2,
            CounterKey = "test_counter_2"
        };

        var usersDataDbConfig = new UsersDataDbConfig
        {
            DatabaseName = _testDatabaseName
        };

        var configOptions = Options.Create(config);
        var dbConfigOptions = Options.Create(usersDataDbConfig);
        var generator = new UserIdGenerator<UserId>(configOptions, _mongoClient, dbConfigOptions);

        var userId = await generator.New();

        Assert.Equal("3200000001", userId.Value);
    }

    [Fact(DisplayName = "Параллельные вызовы генератора возвращают уникальные ID без дублирования")]
    public async Task New_ConcurrentCalls_ReturnsUniqueIds()
    {
        const int concurrentCallsCount = 100;
        var tasks = new List<Task<UserId>>();

        for (int i = 0; i < concurrentCallsCount; i++)
        {
            tasks.Add(_generator.New());
        }

        var results = await Task.WhenAll(tasks);
        var uniqueIds = results.Select(x => x.Value).Distinct().ToList();

        Assert.Equal(concurrentCallsCount, uniqueIds.Count);

        for (int i = 0; i < concurrentCallsCount; i++)
        {
            var expectedId = $"71{(i + 1):D8}";
            Assert.Contains(expectedId, uniqueIds);
        }
    }

    public void Dispose()
    {
        _mongoClient.DropDatabase(_testDatabaseName);
    }
}
