using AwhGameServer.Application.Abstractions.Messaging;
using AwhGameServer.Application.Abstractions.Services;
using AwhGameServer.Application.Abstractions.Stores;
using AwhGameServer.Application.Abstractions.UnitOfWork;

namespace AwhGameServer.Application.UseCases.Authentication;

/// <summary>
/// Обработчик команды обновления токенов авторизации (refresh token flow).
/// </summary>
/// <remarks>
/// Реализует процесс получения новой пары access/refresh токенов при действительной сессии пользователя.
/// 
/// Логика работы:
/// <list type="number">
/// <item>Проверяет, что refresh token не пустой.</item>
/// <item>Вычисляет хэш refresh token и ищет связанную сессию в <see cref="IAuthSessionStore"/>.</item>
/// <item>Проверяет, что сессия существует, не отозвана и не истекла.</item>
/// <item>Загружает точку входа (<see cref="AuthIdentity"/>) и проверяет, что метод аутентификации разрешён для входа.</item>
/// <item>Генерирует новую пару токенов и продлевает сессию (ротация refresh token).</item>
/// </list>
/// </remarks>
public class RefreshCommandHandler(
    IAuthenticationUow uow,
    IAuthTokensGenerator authTokensGenerator,
    ITokenHasher tokenHasher,
    IAuthSessionStore authSessionStore) 
    : ICommandHandler<RefreshCommand, RefreshCommandResult>
{
    public async Task<RefreshCommandResult> Handle(RefreshCommand command, CancellationToken ct)
    {
        if (string.IsNullOrWhiteSpace(command.RefreshToken))
            throw new ArgumentException("Refresh token cannot be null or empty", nameof(command.RefreshToken));
        
        var refreshTokenHash = await tokenHasher.HashToken(command.RefreshToken, ct);
        
        var session = await authSessionStore.GetSessionByRefreshHashAsync(refreshTokenHash, ct);
        
        if (session is null)
            throw new ArgumentException("Refresh token is invalid or expired", nameof(command.RefreshToken));
        
        if (session.IsRevoked)
            throw new ArgumentException("Refresh token is revoked", nameof(command.RefreshToken));
        
        if (session.ExpiresAtUtc < DateTime.UtcNow)
            throw new ArgumentException("Refresh token is expired", nameof(command.RefreshToken));
        
        var authIdentity = await uow.AuthIdentityRepository.GetByIdAsync(session.AuthIdentityId, ct);
        
        if (authIdentity is null)
            throw new ArgumentException("Auth identity not found", nameof(command.RefreshToken));
            
        var authMethodsConfig = await uow.AuthMethodsConfigRepository.GetConfigAsync(ct);
        
        if (!authMethodsConfig.IsLoginMethodAllowed(authIdentity.AuthMethod))
            throw new ArgumentException($"Login with {authIdentity.AuthMethod} is not allowed", nameof(command.RefreshToken));
        
        var authTokens = await authTokensGenerator.GenerateAuthTokens(session.SessionId, authIdentity.UserId, ct);
        
        var newRefreshTokenHash = await tokenHasher.HashToken(authTokens.RefreshToken, ct);
        
        await authSessionStore.RotateRefreshAsync(
            refreshTokenHash, 
            newRefreshTokenHash, 
            authTokens.RefreshTokenExpiresAtUtc, // Продлеваем сессию на то же время, что и refresh token
            ct);
        
        return new RefreshCommandResult(authTokens.AccessToken, authTokens.RefreshToken);
    }
}
