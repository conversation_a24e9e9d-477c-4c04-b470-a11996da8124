using Microsoft.EntityFrameworkCore;
using AwhGameServer.Domain.Aggregates.Game;
using AwhGameServer.Domain.Entities.Game;
using AwhGameServer.Application.Abstractions.Repositories;
using AwhGameServer.Infrastructure.Persistence;
using AwhGameServer.Infrastructure.Persistence.Models.GameData;

namespace AwhGameServer.Infrastructure.Repositories;

/// <summary>
/// Реализация репозитория для чтения конфигурации методов аутентификации.
/// Обеспечивает доступ к данным <see cref="AuthMethodsConfig"/> через Entity Framework и MongoDB.
/// </summary>
/// <param name="context">Контекст базы игровых данных.</param>
public class AuthMethodsConfigReadRepository(GameDataDbContext context) : IAuthMethodsConfigReadRepository
{
    /// <inheritdoc />
    public async Task<AuthMethodsConfig> GetConfigAsync(CancellationToken cancellationToken = default)
    {
        var documents = await context.AuthMethods.ToListAsync(cancellationToken);
        
        var authMethods = documents.Select(MapToDomain).ToList();
        
        return new AuthMethodsConfig(authMethods);
    }

    /// <summary>
    /// Преобразует документ MongoDB в доменную модель.
    /// </summary>
    /// <param name="document">Документ из базы данных.</param>
    /// <returns>Доменная модель метода аутентификации.</returns>
    private static AuthMethod MapToDomain(AuthMethodDocument document)
    {
        return new AuthMethod(
            document.MethodKey,
            document.IsRegistrationAllowed,
            document.IsLoginAllowed
        );
    }
}
