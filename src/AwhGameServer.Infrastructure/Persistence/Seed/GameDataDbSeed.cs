using AwhGameServer.Infrastructure.Persistence.Models.GameData;

namespace AwhGameServer.Infrastructure.Persistence.Seed;

/// <summary>
/// Модель данных для десериализации JSON-файла с начальными данными базы игровых данных.
/// Используется при первом создании БД для загрузки базовых настроек системы.
/// </summary>
public class GameDataDbSeed
{
    public AuthMethodDocument[]? AuthMethods { get; set; }
}
