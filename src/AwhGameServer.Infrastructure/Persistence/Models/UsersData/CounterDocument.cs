using MongoDB.Bson;

namespace AwhGameServer.Infrastructure.Persistence.Models.UsersData;

/// <summary>
/// Документ MongoDB для хранения счетчиков в базе пользовательских данных.
/// Используется для генерации последовательных номеров.
/// Обеспечивает атомарное инкрементирование значений через операции MongoDB.
/// </summary>
public class CounterDocument
{
    /// <summary>
    /// Уникальный идентификатор документа в MongoDB.
    /// </summary>
    public ObjectId Id { get; set; }

    /// <summary>
    /// Ключ счетчика, используемый для идентификации конкретного счетчика.
    /// Должен быть уникальным.
    /// </summary>
    public string CounterKey { get; set; } = null!;

    /// <summary>
    /// Текущее значение счетчика.
    /// Необходимо получать и инкрементировать атомарно для обеспечения уникальности.
    /// </summary>
    public long Value { get; set; }
}
