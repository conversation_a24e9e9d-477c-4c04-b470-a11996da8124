using MongoDB.Bson;
using AwhGameServer.Domain.Aggregates.Users;

namespace AwhGameServer.Infrastructure.Persistence.Models.UsersData;

/// <summary>
/// Документ MongoDB, представляющий идентичность пользователя в базе пользовательских данных.
/// Связывает пользователя с конкретным методом аутентификации и его токеном.
/// Каждый пользователь может иметь несколько идентичностей для разных методов аутентификации.
/// Представляет собой модель хранения <see cref="AuthIdentity"/> из домена.
/// </summary>
public class AuthIdentityDocument
{
    /// <summary>
    /// Уникальный идентификатор документа в MongoDB.
    /// </summary>
    public ObjectId Id { get; set; }

    /// <summary>
    /// Идентификатор пользователя в системе.
    /// Связывает эту идентичность с конкретным пользователем.
    /// </summary>
    public string UserId { get; set; } = null!;

    /// <summary>
    /// Ключ метода аутентификации, используемого для этой идентичности.
    /// Должен соответствовать одному из методов, определенных в AuthMethodDocument.
    /// </summary>
    public string AuthMethod { get; set; } = null!;

    /// <summary>
    /// Токен или уникальный идентификатор пользователя в рамках данного метода аутентификации.
    /// Для OAuth это может быть внешний ID пользователя, для Guest - сгенерированный токен.
    /// </summary>
    public string AuthToken { get; set; } = null!;
}
