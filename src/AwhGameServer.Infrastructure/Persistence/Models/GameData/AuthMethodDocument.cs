using MongoDB.Bson;
using AwhGameServer.Domain.Entities.Game;

namespace AwhGameServer.Infrastructure.Persistence.Models.GameData;

/// <summary>
/// Документ MongoDB, представляющий метод аутентификации в базе игровых данных.
/// Содержит информацию о доступных способах аутентификации пользователей
/// и их разрешениях на регистрацию и вход в систему.
/// Представляет собой модель хранения <see cref="AuthMethod"/> из домена.
/// </summary>
public class AuthMethodDocument
{
    /// <summary>
    /// Уникальный идентификатор документа в MongoDB.
    /// </summary>
    public ObjectId Id { get; set; }

    /// <summary>
    /// Уникальный ключ метода аутентификации.
    /// Примеры: "Guest", "OAuth2.0_GooglePlayGames", "OAuth2.0_AppleGameCenter".
    /// </summary>
    public string MethodKey { get; set; } = null!;

    /// <summary>
    /// Разрешена ли регистрация новых пользователей через этот метод аутентификации.
    /// </summary>
    public bool IsRegistrationAllowed { get; set; }

    /// <summary>
    /// Разрешен ли вход существующих пользователей через этот метод аутентификации.
    /// </summary>
    public bool IsLoginAllowed { get; set; }
}
