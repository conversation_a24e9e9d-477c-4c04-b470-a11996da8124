using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MongoDB.EntityFrameworkCore.Extensions;
using AwhGameServer.Infrastructure.Persistence.Models.UsersData;

namespace AwhGameServer.Infrastructure.Persistence.ModelsConfigurations.UsersData;

/// <summary>
/// Конфигурация Entity Framework для сущности AuthIdentityDocument.
/// Определяет маппинг между C# классом и коллекцией MongoDB,
/// включая имена полей, индексы и ограничения.
/// </summary>
public class AuthIdentityDocumentConfiguration
    : IEntityTypeConfiguration<AuthIdentityDocument>
{
    public void Configure(EntityTypeBuilder<AuthIdentityDocument> builder)
    {
        builder.ToCollection("auth_identities");

        builder
            .HasKey(x => x.Id);

        builder
            .HasIndex(x => x.UserId)
            .IsUnique();
        builder
            .Property(x => x.UserId)
            .HasElementName("user_id")
            .IsRequired();

        builder
            .Property(x => x.AuthMethod)
            .HasElementName("auth_method")
            .IsRequired();

        builder
            .Property(x => x.AuthToken)
            .HasElementName("auth_token")
            .IsRequired();
    }
}
