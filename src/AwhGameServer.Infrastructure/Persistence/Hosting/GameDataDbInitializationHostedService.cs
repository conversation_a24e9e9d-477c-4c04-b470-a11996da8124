using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using AwhGameServer.Infrastructure.Configurations;
using AwhGameServer.Infrastructure.Persistence.Seed;

namespace AwhGameServer.Infrastructure.Persistence.Hosting;

/// <summary>
/// Фоновый сервис для инициализации базы данных игровых данных при запуске приложения.
/// Создает БД если она не существует и заполняет её начальными данными.
/// </summary>
/// <param name="serviceProvider">Провайдер сервисов для создания области видимости.</param>
/// <param name="gameDataDbConfig">Конфигурация базы данных игровых данных.</param>
public class GameDataDbInitializationHostedService(
    IServiceProvider serviceProvider,
    IOptions<GameDataDbConfig> gameDataDbConfig)
    : IHostedService
{
    /// <summary>
    /// Выполняется при запуске приложения.
    /// Создает базу данных если она не существует и заполняет её начальными данными.
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        using var scope = serviceProvider.CreateScope();

        var db = scope.ServiceProvider.GetRequiredService<GameDataDbContext>();

        var justCreated = await db.Database.EnsureCreatedAsync(cancellationToken);

        if (!justCreated) return;

        await GameDataDbSeeder.Seed(db, gameDataDbConfig.Value, cancellationToken);
    }

    /// <summary>
    /// Выполняется при остановке приложения. Не требует дополнительных действий.
    /// </summary>
    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}
